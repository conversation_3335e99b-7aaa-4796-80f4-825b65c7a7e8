<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>话术AI座舱SaaS系统架构规划</title>
  <style>
    body { font-family: "微软雅黑", Arial, sans-serif; background: #f7f9fa; color: #222; }
    h1, h2, h3 { color: #2a5d9f; }
    .arch-diagram { background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #eee; padding: 24px; margin-bottom: 32px; }
    .module-list { margin: 0 0 24px 0; padding: 0 0 0 20px; }
    .tech-table { border-collapse: collapse; width: 100%; background: #fff; }
    .tech-table th, .tech-table td { border: 1px solid #d0d7de; padding: 8px 12px; }
    .tech-table th { background: #f0f4f8; }
    .layer-block { margin: 12px 0; padding: 12px; border-radius: 6px; background: #eaf1fb; border-left: 6px solid #2a5d9f; }
    .layer-title { font-weight: bold; color: #2a5d9f; }
    .tenant { color: #fff; background: #2a5d9f; padding: 2px 8px; border-radius: 4px; font-size: 0.95em; }
    .diagram-box { text-align: center; margin: 24px 0; }
    .diagram-layer { display: block; margin: 0 auto 8px auto; padding: 8px 0; width: 60%; min-width: 320px; background: #eaf1fb; border: 2px solid #2a5d9f; border-radius: 8px; font-weight: bold; color: #2a5d9f; }
    .diagram-arrow { font-size: 24px; color: #2a5d9f; }
  </style>
</head>
<body>
  <h1>话术AI座舱SaaS系统架构规划</h1>

  <div class="arch-diagram">
    <h2>一、系统架构图</h2>
    <div class="diagram-box">
      <div class="diagram-layer">顶层AI能力层<br>（实时质检、实时辅助、话术助手等）</div>
      <div class="diagram-arrow">↓</div>
      <div class="diagram-layer">AI编排中台层<br>（AI流程编排、多模型调度、插件、会话/租户/计费管理）</div>
      <div class="diagram-arrow">↓</div>
      <div class="diagram-layer">底座接入层<br>（运营商线路SIP/IMS/呼叫中心、会话接入、租户隔离）</div>
      <div class="diagram-arrow">↓</div>
      <div class="diagram-layer">前端座舱层<br>（多租户Web座舱、坐席管理、实时交互）</div>
    </div>
  </div>

  <div class="arch-diagram">
    <h2>二、业务模块划分</h2>
    <ul class="module-list">
      <li><span class="tenant">租户管理</span>：租户注册、隔离、权限、资源分配</li>
      <li>会话接入：对接运营商线路，支持多种协议（SIP/IMS/WebRTC）</li>
      <li>AI编排：多模型调度、流程编排、插件扩展</li>
      <li>实时质检：通话内容实时分析、违规检测、评分</li>
      <li>实时辅助：话术推荐、知识推送、智能应答</li>
      <li>话术助手：话术库管理、智能生成、个性化定制</li>
      <li>计费系统：按坐席license计费、用量统计、账单管理</li>
      <li>数据分析：会话数据、质检报表、运营分析</li>
      <li>API网关：对外API、Webhook、第三方集成</li>
    </ul>
  </div>

  <div class="arch-diagram">
    <h2>三、技术方案建议</h2>
    <table class="tech-table">
      <tr>
        <th>层级</th>
        <th>技术选型</th>
        <th>说明</th>
      </tr>
      <tr>
        <td>前端座舱</td>
        <td>React/Vue + Ant Design/Element UI</td>
        <td>多租户Web座舱，实时交互，支持WebRTC</td>
      </tr>
      <tr>
        <td>AI编排中台</td>
        <td>Node.js/Python + 微服务架构（K8s）</td>
        <td>AI流程编排、模型调度、插件化扩展</td>
      </tr>
      <tr>
        <td>实时质检/辅助</td>
        <td>Python/Java + ASR/NLP模型（如OpenAI、科大讯飞）</td>
        <td>实时语音转写、NLP分析、质检与辅助能力</td>
      </tr>
      <tr>
        <td>会话接入</td>
        <td>FreeSWITCH/Asterisk + SIP/IMS/WebRTC</td>
        <td>对接运营商线路，支持多协议，弹性扩展</td>
      </tr>
      <tr>
        <td>租户隔离</td>
        <td>Kubernetes Namespace/多租户DB（如PostgreSQL schema）</td>
        <td>物理/逻辑隔离，保障数据安全</td>
      </tr>
      <tr>
        <td>计费系统</td>
        <td>自研/第三方计费服务</td>
        <td>按坐席license、用量计费，账单自动生成</td>
      </tr>
      <tr>
        <td>API网关</td>
        <td>Kong/Apigee/Nginx</td>
        <td>统一API入口，安全、限流、监控</td>
      </tr>
      <tr>
        <td>部署与运维</td>
        <td>Kubernetes + Docker + Prometheus + ELK</td>
        <td>自动化部署、弹性伸缩、监控告警、日志分析</td>
      </tr>
    </table>
  </div>

  <div class="arch-diagram">
    <h2>四、SaaS化与租户隔离要点</h2>
    <ul class="module-list">
      <li>采用多租户架构，支持租户级别的数据、资源、权限隔离</li>
      <li>租户独立license管理，支持灵活的坐席授权与计费</li>
      <li>支持租户自定义AI能力、话术库、质检规则</li>
      <li>API/数据安全隔离，防止数据越权访问</li>
      <li>支持按需弹性扩容，满足不同租户业务高峰</li>
    </ul>
  </div>
</body>
</html>