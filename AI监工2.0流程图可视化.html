<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI监工2.0系统流程图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
        }
        .description {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 30px;
            border-left: 4px solid #3498db;
        }
        .mermaid {
            text-align: center;
            margin: 30px 0;
        }
        .legend {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .legend-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #e74c3c;
        }
        .legend-item h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .legend-item ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .legend-item li {
            margin: 5px 0;
            color: #34495e;
        }
        .highlight {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .new-feature {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI监工2.0系统流程图</h1>
        
        <div class="description">
            <h3>系统概述</h3>
            <p>AI监工2.0在现有监工链路基础上，新增事后多维度异常监控能力，实现<strong>实时监控</strong>与<strong>事后分析</strong>双重保障机制。系统能够检测9大类异常情况，提供智能化的异常分析和优化建议。</p>
        </div>

        <div class="mermaid">
            flowchart TD
                A[数据入口] --> B{数据来源判断}
                B -->|实时数据流| C[实时监控链路]
                B -->|会话结束数据| D[事后监控链路]
                
                %% 实时监控链路（现有优化）
                C --> C1[意图结果处理]
                C1 --> C2[LLM: 意图初检]
                C1 --> C3[RAG: 意图语料库召回]
                C1 -.-> C4[策略检查<br/>重复检查等]
                
                C2 --> C5[实时异常检测]
                C3 --> C5
                C4 -.-> C5
                
                C5 --> C6{检测到异常?}
                C6 -->|是| C7[LLM: 复检+错误原因分析]
                C6 -->|否| C8[继续监控]
                
                C7 --> C9[企微实时触达]
                C8 --> C10[数据暂存]
                C9 --> C10
                
                %% 事后监控链路（新增核心功能）
                D --> D1[会话数据完整性检查]
                D1 --> D2[多维度异常分析引擎]
                
                D2 --> D3[系统异常检测模块]
                D2 --> D4[业务异常检测模块]
                D2 --> D5[话术质量检测模块]
                
                %% 系统异常检测详细流程
                D3 --> D3a[响应耗时检测<br/>阈值: ≥3秒]
                D3 --> D3b[系统静默检测<br/>阈值: ≥5秒无响应]
                D3 --> D3c[系统报错检测<br/>异常日志分析]
                
                %% 业务异常检测详细流程
                D4 --> D4a[轮次异常检测<br/>系统挂断且≤4轮]
                D4 --> D4b[异常挂断检测<br/>未命中结束语判断]
                D4 --> D4c[通时异常检测<br/>单次通话≥5分钟]
                
                %% 话术质量检测详细流程
                D5 --> D5a[话术匹配检测<br/>LLM评估匹配度]
                D5 --> D5b[意图分类检测<br/>一级意图准确性]
                D5 --> D5c[话术优化检测<br/>更优模型重评估]
                D5 --> D5d[话术评分检测<br/>质量阈值判断]
                
                %% 异常汇总与智能分析
                D3a --> E[异常结果汇总中心]
                D3b --> E
                D3c --> E
                D4a --> E
                D4b --> E
                D4c --> E
                D5a --> E
                D5b --> E
                D5c --> E
                D5d --> E
                
                E --> F[LLM: 综合分析+优化建议生成]
                F --> G[异常分类标记与优先级排序]
                
                G --> H{智能触达策略选择}
                H -->|高优先级| H1[企微即时触达]
                H -->|中优先级| H2[企微批量推送<br/>5分钟内]
                H -->|低优先级| H3[企微批量推送<br/>30分钟内]
                H -->|数据记录| H4[系统埋点落库]
                
                H1 --> I[监控结果展示界面]
                H2 --> I
                H3 --> I
                H4 --> I
                
                %% 数据流转与存储
                C10 --> J[数据汇聚层]
                I --> J
                J --> K[触达系统接口]
                K --> L[异常数据展示<br/>新增异常类型Tab]
                
                %% 样式定义
                style A fill:#ff9999,stroke:#333,stroke-width:3px
                style B fill:#99ccff,stroke:#333,stroke-width:2px
                style C fill:#e1f5fe,stroke:#333,stroke-width:2px
                style D fill:#fff3e0,stroke:#333,stroke-width:2px
                style D2 fill:#f3e5f5,stroke:#333,stroke-width:2px
                style E fill:#e8f5e8,stroke:#333,stroke-width:2px
                style F fill:#fff9c4,stroke:#333,stroke-width:2px
                style G fill:#ffebee,stroke:#333,stroke-width:2px
                style I fill:#e0f2f1,stroke:#333,stroke-width:2px
                style L fill:#f0f8ff,stroke:#333,stroke-width:2px
                
                %% 新增功能高亮
                style D3 fill:#d1ecf1,stroke:#17a2b8,stroke-width:2px
                style D4 fill:#d1ecf1,stroke:#17a2b8,stroke-width:2px
                style D5 fill:#d1ecf1,stroke:#17a2b8,stroke-width:2px
                style H fill:#fff3cd,stroke:#ffc107,stroke-width:2px
        </div>

        <div class="legend">
            <div class="legend-item">
                <h3>🔍 系统异常监控</h3>
                <ul>
                    <li><strong>响应超时</strong>: 灵犀响应时间≥3秒</li>
                    <li><strong>系统静默</strong>: 系统无响应≥5秒</li>
                    <li><strong>系统报错</strong>: 捕获异常日志并分析</li>
                </ul>
            </div>
            
            <div class="legend-item">
                <h3>📊 业务异常监控</h3>
                <ul>
                    <li><strong>轮次过少</strong>: 系统挂断但对话≤4轮</li>
                    <li><strong>异常挂断</strong>: 未命中结束语的系统挂断</li>
                    <li><strong>通时过长</strong>: 单次通话时长≥5分钟</li>
                </ul>
            </div>
            
            <div class="legend-item">
                <h3>💬 话术质量监控</h3>
                <ul>
                    <li><strong>话术不匹配</strong>: 回复与用户意图不符</li>
                    <li><strong>意图分类错误</strong>: 一级意图识别错误</li>
                    <li><strong>话术非最优</strong>: 存在更优话术选择</li>
                    <li><strong>话术评分过低</strong>: 质量评分低于阈值</li>
                </ul>
            </div>
            
            <div class="legend-item highlight">
                <h3>⚡ 实时监控链路</h3>
                <ul>
                    <li>沿用现有监工架构</li>
                    <li>优化LLM调用效率</li>
                    <li>增强异常检测精度</li>
                    <li>保持企微实时触达</li>
                </ul>
            </div>
            
            <div class="legend-item new-feature">
                <h3>🆕 事后分析链路</h3>
                <ul>
                    <li>基于会话结束事件触发</li>
                    <li>多维度并行异常检测</li>
                    <li>智能异常聚合分析</li>
                    <li>分级触达策略</li>
                </ul>
            </div>
            
            <div class="legend-item new-feature">
                <h3>🎯 智能触达策略</h3>
                <ul>
                    <li><strong>高优先级</strong>: 立即企微触达</li>
                    <li><strong>中优先级</strong>: 5分钟内批量推送</li>
                    <li><strong>低优先级</strong>: 30分钟内批量推送</li>
                    <li><strong>数据记录</strong>: 系统埋点落库</li>
                </ul>
            </div>
        </div>

        <div class="description" style="margin-top: 30px;">
            <h3>🚀 实施建议</h3>
            <p><strong>阶段一（1-2周）</strong>: 完善现有实时监控，开发事后监控核心模块</p>
            <p><strong>阶段二（2-3周）</strong>: 集成多维度异常检测，完善触达机制，系统联调</p>
            <p><strong>阶段三（1-2周）</strong>: 灰度发布验证，性能调优，收集反馈优化</p>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });
    </script>
</body>
</html>
