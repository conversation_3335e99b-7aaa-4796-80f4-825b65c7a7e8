import re

@tool
def freemarker_style_renderer(template="", data={}):
    # 处理if-elseif-else结构
    def process_conditions(text, data_dict):
        current = text
        
        while True:
            # Find the start of the first potential top-level <#if ...> tag
            # Use re.DOTALL for the condition part which might span lines, but non-greedy matching
            first_if_match = re.search(r"<#if\s+(.*?)>", current, re.DOTALL)
            if not first_if_match:
                break # No more <#if> tags found

            start_pos = first_if_match.start()
            condition = first_if_match.group(1).strip()
            
            # Find the matching </#if>, respecting nesting level
            nest_level = 0
            search_pos = start_pos  # Start searching from the beginning of the <#if> tag
            end_pos = -1
            
            while search_pos < len(current):
                # Find the next <#if tag (start of condition) or </#if> tag
                if_pattern = r"<#if\s+"
                endif_pattern = r"</#if>"
                
                if_match = re.search(if_pattern, current[search_pos:])
                endif_match = re.search(endif_pattern, current[search_pos:])
                
                next_if_pos = search_pos + if_match.start() if if_match else -1
                next_endif_pos = search_pos + endif_match.start() if endif_match else -1

                # Determine which tag comes first, or if none are left
                if next_if_pos == -1 and next_endif_pos == -1:
                     # This indicates an unmatched <#if> if nest_level > 0
                     if nest_level > 0:
                         start_pos = -1  # Mark as incomplete
                     break

                # Check if the found <#if is the one we started with; only increment level for *nested* <#if tags
                # Need to check if the search position is exactly at the start of the tag we found initially
                is_starting_tag = (search_pos == start_pos and next_if_pos == start_pos)
                
                if next_if_pos != -1 and (next_endif_pos == -1 or next_if_pos < next_endif_pos):
                    # Found an <#if> tag
                    if not is_starting_tag:
                        nest_level = nest_level + 1
                    
                    # Move search position past the tag itself to avoid re-matching it immediately
                    # Find the end '>' of this specific <#if ...> tag
                    tag_end_pos = current.find(">", next_if_pos)
                    if tag_end_pos != -1:
                        search_pos = tag_end_pos + 1
                    else:
                        # No closing '>' found - malformed template
                        start_pos = -1
                        break
                        
                elif next_endif_pos != -1:
                    # Found an </#if> tag
                    if nest_level == 0:
                        # This is the closing tag for the initial <#if>
                        end_pos = next_endif_pos
                        break # Found the complete block
                    else:
                        nest_level = nest_level - 1
                    # Move search position past the </#if> tag
                    search_pos = next_endif_pos + 6
                else:
                     # Logically unreachable if the checks above are correct
                     start_pos = -1
                     break

            if start_pos != -1 and end_pos != -1:
                # Extract the complete top-level if block
                if_block = current[start_pos : end_pos + 6]
                # Process this block (which handles internal elseif/else)
                processed = process_if_block(if_block, data_dict)
                # Replace the original block with the processed content
                current = current[:start_pos] + processed + current[end_pos + 6:]
                # Rescan the modified 'current' from the beginning in the next iteration
            else:
                # No complete top-level block found, or structure error detected
                break # Exit the main processing loop
        
        return current
    
    # 处理单个if块（可能包含elseif和else）
    def process_if_block(if_block, data_dict):
        # 提取if条件
        if_match = re.search(r"<#if\s+(.+?)>", if_block, re.DOTALL)
        if not if_match:
            return ""
            
        if_condition = if_match.group(1).strip()
        
        # 创建一个结构表示if-elseif-else块
        blocks = []
        if_end = if_match.end()
        remaining = if_block[if_end:]
        
        # 查找elseif和else部分
        elseif_positions = []
        else_position = -1
        endif_position = remaining.rfind("</#if>")
        
        # 查找所有顶层的elseif标签
        search_pos = 0
        while search_pos < len(remaining):
            elseif_match = re.search(r"<#elseif\s+(.+?)>", remaining[search_pos:], re.DOTALL)
            if not elseif_match:
                break
                
            # 判断是否为顶层elseif（不在嵌套if内）
            potential_elseif_pos = search_pos + elseif_match.start()
            is_top_level = True
            
            # 检查从if开始到elseif之间的嵌套if数量
            if_count = remaining[:potential_elseif_pos].count("<#if ")
            endif_count = remaining[:potential_elseif_pos].count("</#if>")
            
            if if_count == endif_count:  # 顶层elseif
                elseif_positions.append((potential_elseif_pos, elseif_match.group(1).strip()))
            
            search_pos = search_pos + elseif_match.end()
        
        # 查找顶层else标签
        search_pos = 0
        while search_pos < len(remaining):
            else_match = re.search(r"<#else>", remaining[search_pos:])
            if not else_match:
                break
                
            # 判断是否为顶层else（不在嵌套if内）
            potential_else_pos = search_pos + else_match.start()
            
            # 检查从if开始到else之间的嵌套if数量
            if_count = remaining[:potential_else_pos].count("<#if ")
            endif_count = remaining[:potential_else_pos].count("</#if>")
            
            if if_count == endif_count:  # 顶层else
                else_position = potential_else_pos
                break  # 只有一个顶层else
            
            search_pos = search_pos + else_match.end()
        
        # 处理if部分内容
        if_content_end = elseif_positions[0][0] if elseif_positions else else_position
        if if_content_end == -1:
            if_content_end = endif_position
        
        if_content = remaining[:if_content_end]
        
        # 将if部分添加到块列表
        blocks.append(("if", if_condition, if_content))
        
        # 处理所有elseif部分
        for i, (pos, condition) in enumerate(elseif_positions):
            elseif_start = pos
            
            # 确定当前elseif的内容结束位置
            if i < len(elseif_positions) - 1:
                elseif_content_end = elseif_positions[i+1][0]
            elif else_position != -1:
                elseif_content_end = else_position
            else:
                elseif_content_end = endif_position
            
            elseif_content = remaining[elseif_start + len(f"<#elseif {condition}>"):elseif_content_end]
            
            blocks.append(("elseif", condition, elseif_content))
        
        # 处理else部分
        if else_position != -1:
            else_content = remaining[else_position + len("<#else>"):endif_position]
            
            blocks.append(("else", None, else_content))
        
        # 评估条件并返回相应内容
        for block_type, condition, content in blocks:
            if block_type in ("if", "elseif"):
                result = evaluate_condition(condition, data_dict)
                if result:
                    return content
            else:  # else块
                return content
        
        return ""  # 如果没有条件满足
    
    # 修改evaluate_condition函数以支持??和gt语法
    def evaluate_condition(condition, data_dict):
        # 简化处理 - 优先处理布尔值、变量和基本比较
        condition = condition.strip()
        
        # 1. 处理字面量true/false
        if condition == "true":
            return True
        if condition == "false":
            return False
            
        # 2. 处理变量存在性检查 (例如 <#if user_allow_retention_flg>)
        if condition in data_dict:
            value = data_dict[condition]
            # 确保字符串布尔值转换为实际布尔值
            if value == "true":
                return True
            if value == "false":
                return False
            # 其他类型按Python规则转为布尔值
            return bool(value)
        
        # 3. 处理等于比较 (例如 <#if user_allow_retention_flg == true>)
        if " == " in condition:
            parts = condition.split(" == ")
            left = parts[0].strip()
            right = parts[1].strip()
            
            # 获取左侧值
            if left in data_dict:
                left_value = data_dict[left]
            else:
                left_value = None
                
            # 处理右侧值
            if right == "true":
                right_value = True
            elif right == "false":
                right_value = False
            # 处理数字字面量
            elif right.isdigit():
                right_value = int(right)
            # 处理字符串字面量
            elif (right.startswith('"') and right.endswith('"')) or (right.startswith("'") and right.endswith("'")):
                right_value = right[1:-1]
            elif right in data_dict:
                right_value = data_dict[right]
            else:
                right_value = right
            
            # 进行实际比较
            # 如果左侧是数字字符串，需要转换为数字进行比较
            if isinstance(left_value, str) and left_value.isdigit() and isinstance(right_value, int):
                return int(left_value) == right_value
            return left_value == right_value
            
        # 4. 处理不等于比较 (例如 <#if user_allow_retention_flg != false>)
        if " != " in condition:
            parts = condition.split(" != ")
            left = parts[0].strip()
            right = parts[1].strip()
            
            # 获取左侧值
            if left in data_dict:
                left_value = data_dict[left]
            else:
                left_value = None
                
            # 处理右侧值
            if right == "true":
                right_value = True
            elif right == "false":
                right_value = False
            # 处理数字字面量
            elif right.isdigit():
                right_value = int(right)
            # 处理字符串字面量
            elif (right.startswith('"') and right.endswith('"')) or (right.startswith("'") and right.endswith("'")):
                right_value = right[1:-1]
            elif right in data_dict:
                right_value = data_dict[right]
            else:
                right_value = right
            
            # 进行实际比较
            # 如果左侧是数字字符串，需要转换为数字进行比较
            if isinstance(left_value, str) and left_value.isdigit() and isinstance(right_value, int):
                return int(left_value) != right_value
            return left_value != right_value
            
        # 5. 处理gt/lt数值比较 (例如 <#if user_insurance_size gt 5>)
        if " gt " in condition:
            parts = condition.split(" gt ")
            left = parts[0].strip()
            right = parts[1].strip()
            
            # 获取左侧值并尝试转换为数值
            if left in data_dict:
                left_value = data_dict[left]
                # 如果左侧值是字符串但可以转换为数字，进行转换
                if isinstance(left_value, str) and left_value.isdigit():
                    left_value = int(left_value)
            else:
                try:
                    left_value = int(left)
                except ValueError:
                    try:
                        left_value = float(left)
                    except ValueError:
                        left_value = 0
                        
            # 获取右侧值并尝试转换为数值
            if right in data_dict:
                right_value = data_dict[right]
                # 如果右侧值是字符串但可以转换为数字，进行转换
                if isinstance(right_value, str) and right_value.isdigit():
                    right_value = int(right_value)
            else:
                try:
                    right_value = int(right)
                except ValueError:
                    try:
                        right_value = float(right)
                    except ValueError:
                        right_value = 0
            
            # 进行数值比较
            try:
                return float(left_value) > float(right_value)
            except (ValueError, TypeError):
                return False
        
        # 新增：处理 lt 比较 (小于)
        if " lt " in condition:
            parts = condition.split(" lt ")
            left = parts[0].strip()
            right = parts[1].strip()
            
            # 获取左侧值并尝试转换为数值
            if left in data_dict:
                left_value = data_dict[left]
                # 如果左侧值是字符串但可以转换为数字，进行转换
                if isinstance(left_value, str) and left_value.isdigit():
                    left_value = int(left_value)
            else:
                try:
                    left_value = int(left)
                except ValueError:
                    try:
                        left_value = float(left)
                    except ValueError:
                        left_value = 0
                        
            # 获取右侧值并尝试转换为数值
            if right in data_dict:
                right_value = data_dict[right]
                # 如果右侧值是字符串但可以转换为数字，进行转换
                if isinstance(right_value, str) and right_value.isdigit():
                    right_value = int(right_value)
            else:
                try:
                    right_value = int(right)
                except ValueError:
                    try:
                        right_value = float(right)
                    except ValueError:
                        right_value = 0
            
            # 进行数值比较
            try:
                return float(left_value) < float(right_value)
            except (ValueError, TypeError):
                return False
                
        # 6. 处理 && (AND) 条件 - 短路判断 (例如 <#if user_allow_retention_flg && user_insurance_flg == "normal">)
        if " && " in condition:
            parts = condition.split(" && ", 1)  # 仅分割第一个&&，以处理嵌套
            left = parts[0].strip()
            right = parts[1].strip()
            
            # 先计算左侧
            left_result = evaluate_condition(left, data_dict)
            
            # 短路判断: 如果左侧为False，整个表达式为False，不再计算右侧
            if not left_result:
                return False
                
            # 计算右侧
            right_result = evaluate_condition(right, data_dict)
            
            # 返回组合结果
            return left_result and right_result
            
        # 7. 处理 || (OR) 条件 - 短路判断 (例如 <#if user_allow_retention_flg || user_insurance_flg == "gift">)
        if " || " in condition:
            parts = condition.split(" || ", 1)  # 仅分割第一个||，以处理嵌套
            left = parts[0].strip()
            right = parts[1].strip()
            
            # 先计算左侧
            left_result = evaluate_condition(left, data_dict)
            
            # 短路判断: 如果左侧为True，整个表达式为True，不再计算右侧
            if left_result:
                return True
                
            # 计算右侧
            right_result = evaluate_condition(right, data_dict)
            
            # 返回组合结果
            return left_result or right_result
        
        # 8. 默认情况下返回False，符合FreeMarker的行为
        return False
    
    # 修复get_value函数 - 重写逻辑顺序和实现
    def get_value(expr, data_dict, literal_check=False):
        expr = expr.strip()

        # 1. Handle Literals if requested
        if literal_check:
            if (expr.startswith('"') and expr.endswith('"')) or (expr.startswith("'") and expr.endswith("'")):
                return expr[1:-1]
            if expr == 'true': 
                return True
            if expr == 'false': 
                return False
            # Check numeric after boolean/string
            try: 
                val = int(expr)
                return val
            except ValueError: pass
            try: 
                val = float(expr)
                return val
            except ValueError: pass
            # If literal_check=True but wasn't a literal, fall through to variable lookup

        # 2. Handle Default Operator (!)
        default_value = None
        has_default_operator = False
        base_expr = expr
        # Use regex that stops at the *last* ! not inside quotes (hard for regex)
        # Simpler: find last !, check if it seems like an operator
        last_bang_pos = expr.rfind('!')
        if last_bang_pos != -1:
            # Heuristic: Assume it's default op if not immediately after operator char like =, > etc.
            # Or if it's the last char, or followed by non-path-like chars.
            # Let's try a simpler regex again focusing on the structure.
            default_match = re.match(r"^(.*)!(\s*(.*))?$", expr)
            if default_match:
                 potential_base = default_match.group(1).strip()
                 default_part_str = default_match.group(3) # Group 3 is everything after !
                 # Check if base part looks valid (simplistic check)
                 if potential_base and not potential_base.endswith('='): 
                     has_default_operator = True
                     base_expr = potential_base
                     if default_part_str is not None:
                         default_part_str = default_part_str.strip()
                         if not default_part_str: # var!
                             default_value = ""
                         else: # var!defaultValueExpr
                             # Evaluate the default value expression itself
                             default_value = get_value(default_part_str, data_dict, literal_check=True)
                     else: # var! (implies empty string default)
                          default_value = ""
                 # else: Matched !, but likely part of comparison, ignore.
        
        # Use the expression before potential '!' for lookup
        lookup_expr = base_expr 

        # 3. Handle ?size suffix
        apply_size = False
        if lookup_expr.endswith("?size"):
            apply_size = True
            lookup_expr = lookup_expr[:-5].strip()
            if not lookup_expr: # Just "?size" is invalid
                 final_val = None
                 lookup_ok = False
                 if has_default_operator:
                     return default_value
                 else:
                     return None

        # 4. 执行变量查找 - 特别处理布尔值
        final_val = None
        lookup_ok = True

        # 简单变量直接查找
        if '.' not in lookup_expr and '[' not in lookup_expr:
            if lookup_expr in data_dict:
                final_val = data_dict[lookup_expr]
                # 确保布尔值被正确处理
                if final_val == "true":
                    final_val = True
                elif final_val == "false": 
                    final_val = False
                lookup_ok = True
            else:
                lookup_ok = False
        else:
            # 复杂路径查找
            try:
                current_val = data_dict
                path_parts = lookup_expr.split('.')
                
                for i, part in enumerate(path_parts):
                    part = part.strip()
                    if not part: 
                        continue

                    # 处理数组/列表索引访问 [index]
                    index_match = re.match(r"(\w+)\[(.+)\]", part)
                    if index_match:
                        var_name = index_match.group(1)
                        index_expr = index_match.group(2).strip()
                        
                        # 检查集合存在
                        if isinstance(current_val, dict) and var_name in current_val:
                            current_list_or_dict = current_val[var_name]
                        else:
                            lookup_ok = False
                            break
                            
                        # 确定索引值
                        if index_expr.isdigit():
                            idx = int(index_expr)
                        else:
                            index_val = get_value(index_expr, data_dict)
                            if isinstance(index_val, int):
                                idx = index_val
                            else:
                                lookup_ok = False
                                break
                                
                        # 访问集合元素
                        if isinstance(current_list_or_dict, (list, tuple)) and 0 <= idx < len(current_list_or_dict):
                            current_val = current_list_or_dict[idx]
                        elif isinstance(current_list_or_dict, dict) and isinstance(index_val, str) and index_val in current_list_or_dict:
                            current_val = current_list_or_dict[index_val]
                        else:
                            lookup_ok = False
                            break
                    # 普通字典访问
                    elif isinstance(current_val, dict) and part in current_val:
                        current_val = current_val[part]
                        # 确保在路径中的布尔值也正确处理
                        if current_val == "true": 
                            current_val = True
                        elif current_val == "false":
                            current_val = False
                    # 数字索引访问列表/元组
                    elif isinstance(current_val, (list, tuple)) and part.isdigit():
                        idx = int(part)
                        if 0 <= idx < len(current_val):
                            current_val = current_val[idx]
                        else:
                            lookup_ok = False
                            break
                    else:
                        lookup_ok = False
                        break
                
                if lookup_ok:
                    final_val = current_val

            except Exception as e:
                lookup_ok = False

        # 5. 应用?size后缀（如果需要）
        if apply_size:
            if lookup_ok:
                if final_val is None:
                    final_val = 0
                elif isinstance(final_val, (list, tuple, dict, str)):
                    final_val = len(final_val)
                else:
                    final_val = 0
                lookup_ok = True
            else:
                final_val = 0
                
        # 6. 应用默认值（如果查找失败或结果为None）
        if has_default_operator and (not lookup_ok or final_val is None):
            return default_value
        
        # 7. 返回最终值
        if not lookup_ok:
            return None
        else:
            return final_val

    # 处理变量替换
    def process_variables(text, data_dict):
        result = text
        start_tag = "${"
        end_tag = "}"
        
        # 替换所有变量
        while start_tag in result and end_tag in result:
            var_start = result.find(start_tag)
            var_end = result.find(end_tag, var_start) + 1
            
            if var_start >= 0 and var_end > var_start:
                var_expr = result[var_start+2:var_end-1].strip()
                
                # 检查是否有简单的算术表达式
                arithmetic_match = re.match(r"^(\w+)\s*([+\-*/])\s*(\d+)$", var_expr)
                if arithmetic_match:
                    var_name = arithmetic_match.group(1)
                    operator = arithmetic_match.group(2)
                    number = int(arithmetic_match.group(3))
                    
                    # 获取变量值
                    var_value = get_value(var_name, data_dict)
                    
                    # 如果值可以转换为数字，执行算术运算
                    try:
                        if var_value is not None:
                            if isinstance(var_value, str) and var_value.isdigit():
                                var_value = int(var_value)
                            
                            if isinstance(var_value, (int, float)):
                                if operator == '+':
                                    value = var_value + number
                                elif operator == '-':
                                    value = var_value - number
                                elif operator == '*':
                                    value = var_value * number
                                elif operator == '/':
                                    value = var_value / number
                                else:
                                    value = var_value
                            else:
                                value = var_value
                        else:
                            value = ""
                    except (ValueError, TypeError):
                        value = var_value
                else:
                    # 普通变量查找
                    value = get_value(var_expr, data_dict)
                
                # 如果值为None，使用空字符串
                if value is None:
                    value = ""
                    
                result = result[:var_start] + str(value) + result[var_end:]
            else:
                break
        
        return result

    # 处理list循环结构
    def process_lists(text, data_dict):
        current = text
        
        # 处理嵌套list
        while "<#list " in current and "</#list>" in current:
            # 找到完整的list块
            i = 0
            start_pos = -1
            nested_level = 0
            
            while i < len(current):
                if current[i:i+7] == "<#list ":
                    if nested_level == 0:
                        start_pos = i
                    nested_level = nested_level + 1
                    i = i + 7
                elif current[i:i+8] == "</#list>":
                    nested_level = nested_level - 1
                    if nested_level == 0 and start_pos != -1:
                        # 提取并处理list块
                        list_block = current[start_pos:i+8]
                        processed = process_list_block(list_block, data_dict)
                        # 替换原始块
                        current = current[:start_pos] + processed + current[i+8:]
                        # 重新开始扫描
                        break
                    i = i + 8
                else:
                    i = i + 1
            
            # 如果没有找到完整的list块，则退出循环
            if nested_level != 0 or start_pos == -1:
                break
        
        return current
    
    # 处理单个list块
    def process_list_block(list_block, data_dict):
        # 提取list定义
        list_start = list_block.find("<#list ") + 7
        list_end = list_block.find(">", list_start)
        list_definition = list_block[list_start:list_end].strip()
        
        # 解析"collection as item"格式
        parts = list_definition.split(" as ")
        if len(parts) != 2:
            return "<!-- 无效的list定义 -->"
        
        collection_expr = parts[0].strip()
        item_var = parts[1].strip()
        
        # 获取集合数据
        collection = get_value(collection_expr, data_dict)
        if not collection or not isinstance(collection, (list, tuple)):
            return ""  # 如果集合为空或不是列表类型，返回空字符串
        
        # 提取list内容模板
        template_content = list_block[list_end+1:list_block.rfind("</#list>")]
        
        # 渲染结果
        result = []
        for index, item in enumerate(collection):
            # 创建新的数据上下文，包含当前项和索引
            context = data_dict.copy()
            context.update({
                item_var: item,
                f"{item_var}_index": index
            })
            
            # 处理当前项的模板
            item_content = template_content
            
            # 特殊处理：替换${item_var}_index+1表达式
            # 使用一个临时的占位符进行替换，这个占位符在渲染后会被替换回来
            placeholder = f"###{item_var}_index_plus_one###"
            pattern = r"\$\{" + re.escape(f"{item_var}_index") + r"\+1\}"
            item_content = re.sub(pattern, placeholder, item_content)
            
            # 先处理条件结构
            item_content = process_conditions(item_content, context)
            # 然后处理变量
            item_content = process_variables(item_content, context)
            
            # 最后替换占位符为实际值
            item_content = item_content.replace(placeholder, str(index + 1))
            
            result.append(item_content)
        
        return "".join(result)
    
    # 执行模板处理
    processed = process_conditions(template, data)
    processed = process_lists(processed, data)
    return process_variables(processed, data)

# Helper function to find the end boundary of content within an if/elseif/else clause.
# Tries a simpler approach: find the *first* relevant delimiter after the content starts.
def find_accurate_block_end(full_if_block_text, content_start_pos):
    search_area = full_if_block_text[content_start_pos:]
    
    # Find the first occurrence of tags that could delimit the end of the current content block
    # We are looking for the *next* clause at the *same level*
    elseif_match = re.search(r"<#elseif\s", search_area)
    else_match = re.search(r"<#else>", search_area)
    endif_match = re.search(r"</#if>", search_area) # Find the first endif after content start

    # Find relative positions
    elseif_pos_rel = elseif_match.start() if elseif_match else -1
    else_pos_rel = else_match.start() if else_match else -1
    endif_pos_rel = endif_match.start() if endif_match else -1

    # Collect the positions of the found delimiters
    possible_ends_rel = []
    if elseif_pos_rel != -1: possible_ends_rel.append(elseif_pos_rel)
    if else_pos_rel != -1: possible_ends_rel.append(else_pos_rel)
    if endif_pos_rel != -1: possible_ends_rel.append(endif_pos_rel)
    
    if not possible_ends_rel:
        # No delimiter found after content start - should not happen in a valid block
        # Maybe the block is just <#if cond></#if>?
        # Let's default to the length of the full block text in this edge case or error
        # Try finding the last endif of the original block as fallback
        final_endif_pos_in_block = full_if_block_text.rfind("</#if>")
        return final_endif_pos_in_block if final_endif_pos_in_block >= content_start_pos else len(full_if_block_text)

    # Find the earliest delimiter
    first_delimiter_rel = min(possible_ends_rel)
    
    # The content ends just before this delimiter starts
    end_pos_abs = content_start_pos + first_delimiter_rel
    return end_pos_abs