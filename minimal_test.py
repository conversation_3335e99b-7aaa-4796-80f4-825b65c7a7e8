from freemarker_skill import freemarker_style_renderer

# 创建一个造好的测试用例
test_data = {
    "template": "<#if user_insurance_size == 1>这是单一保单<#else>这是多个保单</#if>",
    "data": {
        "user_insurance_flg": "normal", 
        "user_allow_retention_flg": True,
        "user_allow_reduce_flg": True,
        "user_insurance_size": 1
    }
}

# 测试数值为整数1的情况
print("开始测试整数1...")
result1 = freemarker_style_renderer(test_data["template"], test_data["data"])
print("整数1结果:", result1)

# 测试数值为字符串"1"的情况
print("开始测试字符串1...")
test_data["data"]["user_insurance_size"] = "1"
result2 = freemarker_style_renderer(test_data["template"], test_data["data"])
print("字符串1结果:", result2)

# 完整的测试用例
complete_test = {
    "template": """###要求：
当客户提出以下任意一点需求时，判断客户是需要退保的：
1. 要求退保
2. 麻烦你帮忙办理退保
3. 要求解除或终止保险
4. 要求降级到基础版
5. 要求下个月终止扣费
6. 询问退保流程或手续
7. 要求你发送退保链接
**严格按照以下的交互轮次执行，不同的挽留轮次不能同时发出，**
**发送给用户的话中，绝对不得提及用户情绪、经济状况、这是第几次挽留**
**如果用户是因为被保人去世，则无需挽留，直接发送自助退保话术和小程序链接，并转人工**
重点注意：
1. 在任何时候，一旦发现用户存在情绪激动、辱骂坐席、涉及投诉、提到举报的情况，则立即进入引导用户退保流程，返回step_code: user_insurance_normal_send_refund_link
2. 在引导用户退保，发送退保链接后，若用户仍情绪激动、涉及投诉，则返回转人工，step_code: transfer_to_human
3. 在无法确定下一步的流程时，默认返回当前步骤流程，再与用户交互一轮
当前业务具体步骤如下：
<#if user_insurance_flg == "none">Step 1.1：用户无保单时
step_code: user_insurance_none
话术内容：告诉用户无保单，并咨询用户是否有其他手机号
Step 1.1.1：用户回复1.1，无其他手机号
step_code: user_insurance_none_reply_none
话术内容：再次向客户解释，查询到名下无保单，辛苦客户再核实一下
<#elseif user_insurance_flg == "gift">Step 1.2: 用户仅有赠险
step_code: user_insurance_gift
话术内容：告知用户保单为福利，不收取任何费用，并咨询用户是否有其他手机号
Step 1.2.1：用户回复1.2，无其他手机号
step_code: user_insurance_gift_reply_none
话术内容：再次解释保单不会收费，福利保单，且可以给予用户保障，并发送电子保单链接
Step 1.2.2：用户回复1.2，不退保了
step_code: user_insurance_gift_reply_not_retention
话术内容：服务结束话术，如：好的，已为您保留当前保单，后续xxx
<#elseif user_insurance_flg == "normal">确认保单：
<#if user_insurance_size == 1>Step 1.3.1：跟用户确认退保保单
step_code: user_insurance_normal_ask_confirm_refund_policy
话术内容：发送产品名称、金额、被保人，询问用户是否确认退保此单
<#else>Step 1.3.2：用户有多张保单，索要欲退保保单信息（保单号、扣费截图、金额等）
step_code: user_insurance_normal_ask_refund_policy_info
话术内容：索要欲退保保单信息（保单号、扣费截图、金额等）
</#if></#if>""",
    "data": {
        "user_insurance_flg": "normal", 
        "user_allow_retention_flg": True,
        "user_allow_reduce_flg": True,
        "user_insurance_size": 1
    }
}

# 测试数值为整数1的情况
print("\n开始测试完整用例(整数1)...")
result3 = freemarker_style_renderer(complete_test["template"], complete_test["data"])
print("完整用例(整数1)结果:", result3)

# 测试数值为字符串"1"的情况
print("\n开始测试完整用例(字符串1)...")
complete_test["data"]["user_insurance_size"] = "1"
result4 = freemarker_style_renderer(complete_test["template"], complete_test["data"])
print("完整用例(字符串1)结果:", result4) 