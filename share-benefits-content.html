        <div class="slide-content">
            <h2>通过分享将Get的技能</h2>
            
            <div style="display: flex; flex-wrap: wrap; justify-content: space-between; margin-top: 30px;">
                <div style="width: 48%; margin-bottom: 30px; background: linear-gradient(135deg, rgba(0,195,137,0.05) 0%, transparent 100%); padding: 20px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);">
                    <div class="section-title">在灵犀平台玩转知识库RAG</div>
                    <p style="font-size: 18px;">掌握三类知识库的使用场景与最佳实践，提升模型回答的准确性与相关性</p>
                </div>
                
                <div style="width: 48%; margin-bottom: 30px; background: linear-gradient(135deg, rgba(0,195,137,0.05) 0%, transparent 100%); padding: 20px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);">
                    <div class="section-title">成为AIGC技能编排高手</div>
                    <p style="font-size: 18px;">学习并行节点、异步调用、节点同名等高级技巧，构建复杂而高效的流程</p>
                </div>
                
                <div style="width: 48%; background: linear-gradient(135deg, rgba(0,195,137,0.05) 0%, transparent 100%); padding: 20px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);">
                    <div class="section-title">开始沉淀测试集</div>
                    <p style="font-size: 18px;">以回滚准确率代替主观感受，实现模型效果的客观评估与持续追踪</p>
                </div>
                
                <div style="width: 48%; background: linear-gradient(135deg, rgba(0,195,137,0.05) 0%, transparent 100%); padding: 20px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);">
                    <div class="section-title">了解我们踩过的坑</div>
                    <p style="font-size: 18px;">学习各个模块的"避坑指南"，减少重复犯错，提升开发效率</p>
                </div>
            </div>
            
            <div class="tip-box" style="margin-top: 20px; position: relative; overflow: hidden;">
                <div class="tip-title">分享目标</div>
                <div class="tip-content">通过本次分享，希望大家能够更加高效地使用灵犀平台，快速构建出高质量的AI应用</div>
                <!-- 添加小图标装饰 -->
                <div style="position: absolute; bottom: 10px; right: 10px; opacity: 0.2;">
                    <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20,5 L35,30 L5,30 Z" fill="#00c389"></path>
                        <text x="20" y="25" text-anchor="middle" fill="#fff" font-size="15">i</text>
                    </svg>
                </div>
            </div>
        </div>
