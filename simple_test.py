try:
    import sys
    print("Python version:", sys.version)
    
    # Basic imports
    print("Importing freemarker_skill...")
    import freemarker_skill
    
    # Very simple test
    print("Running test...")
    template = """<#list items as item>${item_index+1}. Test</#list>"""
    data = {"items": ["A", "B", "C"]}
    result = freemarker_skill.freemarker_style_renderer(template, data)
    print("Result:", result)
    
    print("Done!")
except Exception as e:
    print("Error:", str(e))
    import traceback
    traceback.print_exc() 