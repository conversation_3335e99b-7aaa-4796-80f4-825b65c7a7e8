@tool
def get_step_code(script_step_code=None, ai_step_code=None, step_code_list=[], step_info_list=[]):
    result_step_code = "transfer_to_human"
    
    if script_step_code is not None and script_step_code != "":
        result_step_code = script_step_code
    elif ai_step_code is not None and ai_step_code != "":
        result_step_code = ai_step_code
    
    # 确保step_code_list和step_info_list不为None
    if step_code_list is None:
        step_code_list = []
    
    if step_info_list is None:
        step_info_list = []
    
    # 当step_code_list不为空时
    if step_code_list:
        # 统计result_step_code出现在step_code_list的次数
        count = 0
        for code in step_code_list:
            if code == result_step_code:
                count = count + 1
        
        # 如果超过一次
        if count > 1:
            # 查找匹配的step_info对象
            for step_info in step_info_list:
                if step_info.get("stepCode") == result_step_code:
                    max_rounds = step_info.get("maxRounds")
                    
                    # 检查maxRounds是否存在并转为数字比较
                    if max_rounds is not None and max_rounds != "":
                        try:
                            max_rounds_num = 0
                            if isinstance(max_rounds, str):
                                max_rounds_num = int(max_rounds)
                            else:
                                max_rounds_num = max_rounds
                                
                            # 判断次数是否大于等于maxRounds值
                            if count >= max_rounds_num:
                                max_jump_step = step_info.get("maxJumpStep")
                                if max_jump_step is not None and max_jump_step != "":
                                    result_step_code = max_jump_step
                        except (ValueError, TypeError):
                            pass
                    break
    
    # 将result_step_code添加到step_code_list
    new_step_code_list = step_code_list.copy()
    new_step_code_list.append(result_step_code)
    
    return {
      "step_code": result_step_code,
      "step_code_list": new_step_code_list
    } 

# 意图编码到意图编号的映射
intention_code_to_no = {
    "surrender_insurance": 1,
    "consultation_surrender": 2,
    "query_insurance": 3,
    "noneed_insurance": 4,
    "unrelated_insurance": 5,
    "consult_insurance": 6,
    "consult_compensation": 7,
    "doubt_insuranceprice": 8,
    "sensitive_message": 9,
    "simple_reply": 10,
    "willing_insurance": 11,
    "calling": 12,
    "complaint": 13
}

@tool
def get_first_faq_answer(answer, intentionCode):
    # Check if the input is None or empty
    if not answer:
        return ""
    
    # 根据intentionCode查找对应的intentionNo
    intentionNo = intention_code_to_no.get(intentionCode, "")
    
    # Try to get the 'process' from the first item in the list
    process = ""
    try:
        first_item = answer[0]
        process = first_item.get("process", "")
    except (TypeError, AttributeError):
        process = ""
    
    return {
        "process": process,
        "intentionNo": intentionNo
    }

@tool
def format_freemarker_session(session):
    """
    处理Freemarker模板中变量不存在的问题
    
    Args:
        session: 会话对象，可能包含step_code_list或其他属性
        
    Returns:
        dict: 包含安全处理后的变量
    """
    # 创建一个新的会话对象，确保关键属性存在
    safe_session = {}
    
    # 处理step_code_list，如果不存在则设为空列表
    if hasattr(session, 'step_code_list'):
        safe_session['step_code_list'] = session.step_code_list
    else:
        safe_session['step_code_list'] = []
    
    return safe_session

# 退保相关内容
surrender_info = {
    "content": [
        {
            "content": "您看是继续保留保单还是取消保障，如果您要取消保障可以点击我给您发送的链接自行操作（点击我给您发送的链接--我的保单--选择您需要退保的保单--根据提示填写信息确认）",
            "type": "text"
        },
        {
            "miniChannel": "JJ_MINIAPP_ZAB",
            "miniContent": "点击操作退保",
            "miniImageUrl": "https://nmpcdn.zhonganib.com/nmp/1744360447594/bbe54cb716af11f095c3deef608c53b7.png?&w=700&h=592",
            "miniPath": "/pages/finance/commonTransfer/index.html?pageType=discharge&resourceNo=zayb_dx_fwgj_jjtb&tbtype=jjtb",
            "miniTitle": "点击操作退保",
            "type": "miniprogrampage"
        }
    ]
}