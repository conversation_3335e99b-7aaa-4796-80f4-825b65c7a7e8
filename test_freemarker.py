from freemarker_skill import freemarker_style_renderer
import freemarker_skill as fs

# 测试数值比较
def test_number_comparison():
    # 测试 user_insurance_size == 1 (整数值)
    template1 = "<#if user_insurance_size == 1>单一保单<#else>非单一保单</#if>"
    result1 = freemarker_style_renderer(template1, {"user_insurance_size": 1})
    print("整数值等于1测试:", result1)
    
    # 测试 user_insurance_size == 1 (字符串值)
    template2 = "<#if user_insurance_size == 1>单一保单<#else>非单一保单</#if>"
    result2 = freemarker_style_renderer(template2, {"user_insurance_size": "1"})
    print("字符串值等于1测试:", result2)
    
    # 测试 user_insurance_size gt 1 (整数值)
    template3 = "<#if user_insurance_size gt 1>多个保单<#else>非多个保单</#if>"
    result3 = freemarker_style_renderer(template3, {"user_insurance_size": 2})
    print("整数值大于1测试:", result3)
    
    # 测试 user_insurance_size gt 1 (字符串值)
    template4 = "<#if user_insurance_size gt 1>多个保单<#else>非多个保单</#if>"
    result4 = freemarker_style_renderer(template4, {"user_insurance_size": "2"})
    print("字符串值大于1测试:", result4)

# 测试布尔值条件
def test_boolean_condition():
    # 直接布尔值
    template1 = "<#if user_allow_retention_flg>允许挽留<#else>不允许挽留</#if>"
    result1 = freemarker_style_renderer(template1, {"user_allow_retention_flg": True})
    print("直接布尔值True测试:", result1)
    
    # 字符串布尔值 "true"
    template2 = "<#if user_allow_retention_flg>允许挽留<#else>不允许挽留</#if>"
    result2 = freemarker_style_renderer(template2, {"user_allow_retention_flg": "true"})
    print("字符串布尔值'true'测试:", result2)
    
    # 布尔值比较
    template3 = "<#if user_allow_retention_flg == true>允许挽留<#else>不允许挽留</#if>"
    result3 = freemarker_style_renderer(template3, {"user_allow_retention_flg": True})
    print("布尔值等于True测试:", result3)
    
    # 字符串布尔值比较
    template4 = "<#if user_allow_retention_flg == true>允许挽留<#else>不允许挽留</#if>"
    result4 = freemarker_style_renderer(template4, {"user_allow_retention_flg": "true"})
    print("字符串布尔值等于True测试:", result4)

# 测试复杂组合条件
def test_complex_condition():
    template = """<#if user_insurance_flg == "normal" && user_allow_retention_flg && user_insurance_size == 1>
正常保单+允许挽留+单一保单
<#elseif user_insurance_flg == "normal" && user_allow_retention_flg && user_insurance_size gt 1>
正常保单+允许挽留+多个保单
<#else>
其他情况
</#if>"""
    
    # 测试用例1：正常保单+允许挽留+单一保单
    data1 = {
        "user_insurance_flg": "normal",
        "user_allow_retention_flg": True,
        "user_insurance_size": 1
    }
    result1 = freemarker_style_renderer(template, data1)
    print("复杂组合1测试:", result1)
    
    # 测试用例2：正常保单+允许挽留+多个保单
    data2 = {
        "user_insurance_flg": "normal",
        "user_allow_retention_flg": True,
        "user_insurance_size": 2
    }
    result2 = freemarker_style_renderer(template, data2)
    print("复杂组合2测试:", result2)
    
    # 测试用例3：正常保单+允许挽留+字符串单一保单
    data3 = {
        "user_insurance_flg": "normal",
        "user_allow_retention_flg": True,
        "user_insurance_size": "1"
    }
    result3 = freemarker_style_renderer(template, data3)
    print("复杂组合3测试:", result3)

# 执行测试
if __name__ == "__main__":
    print("===== 数值比较测试 =====")
    test_number_comparison()
    
    print("\n===== 布尔值条件测试 =====")
    test_boolean_condition()
    
    print("\n===== 复杂组合条件测试 =====")
    test_complex_condition()
    
    # 测试原始需求示例
    print("\n===== 原始需求示例测试 =====")
    template = """<#if user_insurance_flg == "normal">
<#if user_insurance_size == 1>这是单一保单<#else>这是多个保单</#if>
</#if>"""
    data = {
        "user_insurance_flg": "normal",
        "user_insurance_size": 1
    }
    result = freemarker_style_renderer(template, data)
    print("原始需求示例(数值1):", result)
    
    data["user_insurance_size"] = "1"
    result = freemarker_style_renderer(template, data)
    print("原始需求示例(字符串'1'):", result)

template = """您申请退保的保单信息如下：
<#list policyList as policy>${policy_index+1}.产品名称：${policy.产品名称}
✅被保人：${policy.被保人姓名}
✅交费金额：${policy.月保费}元/月（年交：${policy.年保费}元/年）
</#list>您看是这几份保单吗？"""

data = {
    "policyList": [
        {
            "产品名称": "众安百万重疾险（旗舰版）",
            "被保人姓名": "*塘军",
            "月保费": "213.0",
            "年保费": 2556
        },
        {
            "产品名称": "优保关爱·百万医疗（加强版）",
            "被保人姓名": "*塘军",
            "月保费": "0.99",
            "年保费": 737.99
        }
    ]
}

result = fs.freemarker_style_renderer(template, data)
print(result) 