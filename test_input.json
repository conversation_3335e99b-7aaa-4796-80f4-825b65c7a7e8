{"template": "您申请退保的保单信息如下：\n<#list policyList as policy>${policy_index+1}.产品名称：${policy.产品名称}\n✅被保人：${policy.被保人姓名}\n✅交费金额：${policy.月保费}元/月（年交：${policy.年保费}元/年）\n</#list>您看是这几份保单吗？", "data": {"policyFileList": [{}], "policyList": [{"保单号": "IH1100557270753424", "订单号": "MARS17321582366640238500", "产品名称": "众安百万重疾险（旗舰版）", "保险止期": "2025-11-21", "月保费": "213.0", "年保费": 2556, "被保人姓名": "*塘军", "保额": 2750000, "险类": "重疾", "下期应交日期": {}, "实缴保费期数": "0期", "投被保人关系": "本人"}, {"保单号": "62403103226G6500191", "订单号": "MARS17320655467150090346", "产品名称": "优保关爱·百万医疗（加强版）", "保险止期": "2025-11-20", "月保费": "0.99", "年保费": 737.99, "被保人姓名": "*塘军", "保额": 15005000, "险类": "医疗", "下期应交日期": {"$ref": "$.insurance[0].nextPaymentDate"}, "实缴保费期数": "0期", "投被保人关系": "本人"}], "保单号": "IH1100557270753424", "订单号": "MARS17321582366640238500", "产品名称": "众安百万重疾险（旗舰版）", "保险止期": "2025-11-21", "月保费": "213.0", "年保费": 2556, "被保人姓名": "*塘军", "保额": 2750000, "险类": "重疾", "下期应交日期": {}, "实缴保费期数": "0期", "投被保人关系": "本人", "手机号后4位": "0529", "退保金额": "0.7"}}