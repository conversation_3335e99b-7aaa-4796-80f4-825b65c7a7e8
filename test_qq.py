from freemarker_skill import freemarker_style_renderer

# 测试??操作符
template1 = "<#if test_var??>变量存在</#if>"
template2 = "<#if missing_var??>变量不存在</#if>"
template3 = '[{ "type": "news" }<#if test_var??>,{ "type": "image" }]</#if>'

data = {
    'test_var': 'hello'
}

print("测试1 (变量存在):", freemarker_style_renderer(template1, data))
print("测试2 (变量不存在):", freemarker_style_renderer(template2, data))
print("测试3 (JSON中的条件):", freemarker_style_renderer(template3, data)) 