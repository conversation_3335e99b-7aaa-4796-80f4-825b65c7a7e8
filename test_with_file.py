import json
import traceback

try:
    print("Loading test data...")
    with open("test_input.json", "r", encoding="utf-8") as f:
        test_data = json.load(f)
    
    print("Importing freemarker_skill...")
    import freemarker_skill
    
    print("Running test...")
    result = freemarker_skill.freemarker_style_renderer(
        test_data["template"], 
        test_data["data"]
    )
    
    print("\nRESULT:")
    print(result)
    
    # 将结果写入文件
    with open("test_output.txt", "w", encoding="utf-8") as f:
        f.write(result)
    
    print("\nTest completed. Result also written to test_output.txt")
    
except Exception as e:
    print(f"Error: {str(e)}")
    traceback.print_exc() 