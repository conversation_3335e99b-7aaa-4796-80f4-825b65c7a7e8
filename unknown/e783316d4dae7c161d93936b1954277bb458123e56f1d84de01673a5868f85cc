def process_text(text):
    """
    处理文本字符串：
    1. 按换行分割
    2. 去掉每行首尾空格
    3. 连续多个空行合并为一个空行
    
    参数:
        text (str): 输入的文本字符串
    
    返回:
        str: 处理后的文本字符串
    """
    # 按换行分割字符串
    lines = text.split('\n')
    
    # 去掉每行首尾空格
    stripped_lines = []
    for line in lines:
        stripped_lines.append(line.strip())
    
    # 合并连续的空行
    result_lines = []
    prev_empty = False
    
    for line in stripped_lines:
        is_empty = len(line) == 0
        
        if is_empty:
            if not prev_empty:
                result_lines.append(line)
            prev_empty = True
        else:
            result_lines.append(line)
            prev_empty = False
    
    # 重新组合成字符串
    result = '\n'.join(result_lines)
    return result


# 测试示例
if __name__ == "__main__":
    # 测试用例
    test_text = """  第一行有前后空格  
    
    
    第二行前面有空格
后面有空格    


连续空行测试

    中间有空格的行    
    
    
最后一行  """
    
    print("原始文本:")
    print(repr(test_text))
    print("\n处理后文本:")
    result = process_text(test_text)
    print(repr(result))
    print("\n实际显示:")
    print(result)
