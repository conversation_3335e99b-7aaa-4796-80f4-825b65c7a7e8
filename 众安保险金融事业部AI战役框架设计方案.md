# 众安保险金融事业部AI战役框架设计方案

## 一、战役概述

### 1.1 战役名称
**"智启未来"** - 众安保险金融事业部AI赋能战役

### 1.2 战役愿景
通过AI战役实现金融业务全方位智能化提效，打造AI驱动的敏捷组织与创新生态。

### 1.3 战役目标
- **短期目标（6个月内）**：提升组织AI氛围，覆盖80%员工的基础AI认知，建立AI应用交流机制
- **中长期目标（1年+）**：形成自驱动的AI创新文化，挖掘并落地3-5个高价值AI应用场景

### 1.4 战役架构
战役设计为"1+3"模式：
- **1个总体框架**：智启未来AI赋能体系
- **3个子项目**：
  - **灵犀课堂**（轰隆课堂）：专注于司内业务AI应用
  - **AI学院**（河畔学院）：专注于个人AI能力提升
  - **创新实验室**（智创空间）：专注于挖掘新的AI业务场景

## 二、组织架构

### 2.1 战役指挥部
- **总指挥**：周杨
- **战役负责人**：各 owner
- **战役秘书处**：负责战役整体协调、资源调配、进度跟踪

### 2.2 子项目组织
1. **灵犀课堂（轰隆课堂）**
   - 负责人：雷雨
   - 核心成员：待定
   - 职责：灵犀平台系列分享、建立AIGC答疑社区

2. **AI学院（河畔学院）**
   - 负责人：何盼
   - 核心成员：待定
   - 职责：AIGC系列分享、AI认知调研、AI创新积分管理

3. **创新实验室（智创空间）**
   - 负责人：刘畅
   - 核心成员：黄骅艺、毛锦程、裴若萱
   - 职责：创新大赛组织、场景挖掘与落地

### 2.3 AI赋能大使
- 在各业务部门选拔AI赋能大使，作为部门与战役的桥梁
- 负责推动部门内AI知识传播、收集反馈、组织参与活动

## 三、子项目详细方案

### 3.1 灵犀课堂（轰隆课堂）

#### 3.1.1 目标
提升灵犀AI工作台的使用率至30%以上，打造业务场景AI应用标杆

#### 3.1.2 核心活动
1. **灵犀平台系列分享**
   - 频率：每2个月至少一次
   - 形式：线上/线下结合，录制视频存档
   - 内容：灵犀平台功能介绍、最佳实践分享、案例解析
   - 讲师：内部专家+外部嘉宾

2. **AIGC答疑社区（灵犀智答）**
   - 建立专属问答社区（可基于企业微信群+知识库）
   - 开发AI助手（Agent），自动回答常见问题
   - 定期整理共性问题与业务AI卡点，形成解决方案库

3. **灵犀实战工坊**
   - 每季度举办一次实战工坊，解决实际业务问题
   - 邀请业务部门提出场景需求，组织跨部门团队协作解决

#### 3.1.3 运营节奏
- 月度：灵犀分享会/答疑汇总
- 季度：实战工坊/成果展示
- 半年：最佳实践评选/成果发布会

### 3.2 AI学院（河畔学院）

#### 3.2.1 目标
覆盖80%员工的基础AI认知，培养AI技能应用能力

#### 3.2.2 核心活动
1. **AI认知调研**
   - 设计分层级的AI认知调查问卷
   - 建立员工AI能力画像，分级分类
   - 根据调研结果定制培训计划

2. **AIGC系列分享**
   - 频率：每2个月至少一次
   - 内容：AI基础知识、提示词工程、行业应用案例
   - 分级设置：入门班、进阶班、专家班

3. **AI创新积分制度**
   - 设立"AI创新积分"体系
   - 积分来源：参与培训、分享知识、应用实践
   - 积分兑换：算力资源、外部培训机会、周边礼品

4. **AI能力认证**
   - 设计AI能力等级认证体系
   - 开展定期认证考核
   - 颁发数字证书，与职业发展挂钩

#### 3.2.3 运营节奏
- 月度：AI技能培训/积分排行
- 季度：积分兑换/能力认证
- 半年：AI达人评选/成果展示

### 3.3 创新实验室（智创空间）

#### 3.3.1 目标
挖掘1-2个高潜力AI应用场景，推动试点落地

#### 3.3.2 核心活动
1. **AI创新大赛**
   - 形式多样：实战演练工坊、AI主题赛、提示词技能大赛
   - 赛制设计：初赛（创意提交）→复赛（方案展示）→决赛（原型演示）
   - 评审机制：专家评审+大众投票

2. **创新孵化营**
   - 为优秀创意提供孵化支持
   - 配备技术导师、业务导师
   - 提供算力资源、开发支持

3. **成果转化机制**
   - 建立创新成果评估体系
   - 设计从创意到产品的转化路径
   - 对接业务部门进行试点应用

#### 3.3.3 运营节奏
- 季度：创新大赛/成果评审
- 半年：孵化项目路演/试点落地

## 四、奖励与激励机制

### 4.1 AI创新积分体系
- **积分获取**：
  - 参与培训与分享：5-20分/次
  - 提交AI应用案例：10-50分/个
  - 解答社区问题：2分/个
  - 创新大赛获奖：50-200分/次

- **积分兑换**：
  - 算力资源：50分=20美刀(只可用于购买AI账号)
  - 外部培训：100分=1次外部AI课程学习机会
  - 专属周边：AI无界限文化衫、定制礼品
  - 荣誉称号：AI创新达人、灵犀大师

### 4.2 讲师激励
- 内部讲师津贴：每次分享提供讲师津贴
- 教师节特别奖励：优秀讲师评选与奖励
- 讲师成长计划：提供外部进修机会

### 4.3 创新奖励
- 创新大赛奖金：设置一、二、三等奖及特别奖
- 落地激励：创新成果成功落地后的业绩分成
- 创新荣誉：年度AI创新团队/个人评选

## 五、资源保障

### 5.1 技术资源
- 阿里云AI算力资源
- 灵犀AI工作台平台支持
- AI助手账号资源

### 5.2 培训资源
- 内部讲师团队
- 外部专家资源库
- 培训课程与材料

### 5.3 奖励预算
- 积分兑换物资
- 创新大赛奖金
- 讲师津贴

## 六、效果跟踪与评估

### 6.1 关键指标（KPI）
1. **覆盖率指标**
   - AI基础认知覆盖率：目标80%
   - 灵犀平台使用率：目标30%

2. **参与度指标**
   - 培训参与人次
   - 社区活跃度
   - 创新提案数量

3. **成果指标**
   - AI应用场景落地数
   - 业务效率提升比例
   - 创新成果转化率

### 6.2 评估机制
- 月度：数据监测与分析
- 季度：阶段性成果评估
- 半年：综合效果评估与调整

### 6.3 反馈与优化
- 建立多渠道反馈机制
- 定期开展满意度调查
- 基于反馈持续优化战役方案

## 七、实施路径

### 7.1 第一阶段（1-2个月）：启动与基础建设
- 组建战役团队
- 开展AI认知调研
- 建立AI答疑社区
- 设计AI创新积分体系

### 7.2 第二阶段（3-4个月）：全面推进
- 灵犀平台系列分享常态化
- AI学院培训体系运行
- 首届创新大赛启动

### 7.3 第三阶段（5-6个月）：成果转化
- 创新成果孵化与落地
- 最佳实践总结与推广
- 半年成果展示与表彰

### 7.4 长期机制（6个月后）
- 建立自驱动的AI创新文化
- 形成可持续的AI能力提升体系
- 构建业务与AI融合的长效机制

## 八、风险与应对

### 8.1 潜在风险
- 员工参与积极性不高
- AI技能学习曲线陡峭
- 创新成果难以落地

### 8.2 应对策略
- 强化激励机制，提高参与吸引力
- 分级设计培训内容，降低学习门槛
- 建立创新-业务对接机制，促进落地
